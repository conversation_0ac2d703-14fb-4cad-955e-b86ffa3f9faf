@echo off

REM Define paths
set ROOT_DIR=%~dp0
set BUILD_DIR=%ROOT_DIR%build
set CONFIG_FILE=%ROOT_DIR%config.json

REM Ensure build directory exists
if not exist "%BUILD_DIR%" (
    mkdir "%BUILD_DIR%"
)

REM Compile find_exe_files.go for multiple platforms
set FIND_EXE_FILES=%ROOT_DIR%find_exe_files\find_exe_files.go
set OUTPUT_DIR=%BUILD_DIR%\find_exe_files
if not exist "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
)

REM Windows AMD64
set GOOS=windows
set GOARCH=amd64
go build -o "%OUTPUT_DIR%\find_exe_files_windows_amd64.exe" "%FIND_EXE_FILES%"
copy "%CONFIG_FILE%" "%OUTPUT_DIR%" >nul

REM Linux AMD64
set GOOS=linux
set GOARCH=amd64
go build -o "%OUTPUT_DIR%\find_exe_files_linux_amd64" "%FIND_EXE_FILES%"
copy "%CONFIG_FILE%" "%OUTPUT_DIR%" >nul

REM Linux ARM64
set GOOS=linux
set GOARCH=arm64
go build -o "%OUTPUT_DIR%\find_exe_files_linux_arm64" "%FIND_EXE_FILES%"
copy "%CONFIG_FILE%" "%OUTPUT_DIR%" >nul

REM Compile email_sender.go for Windows AMD64
set EMAIL_SENDER=%ROOT_DIR%email_sender\email_sender.go
set OUTPUT_DIR=%BUILD_DIR%\email_sender
if not exist "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
)
set GOOS=windows
set GOARCH=amd64
go build -o "%OUTPUT_DIR%\email_sender.exe" "%EMAIL_SENDER%"
copy "%CONFIG_FILE%" "%OUTPUT_DIR%" >nul

REM Compile security_scan_reader.go for Windows AMD64
set SECURITY_SCAN_READER=%ROOT_DIR%security_scan_reader\security_scan_reader.go
set OUTPUT_DIR=%BUILD_DIR%\security_scan_reader
if not exist "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
)
set GOOS=windows
set GOARCH=amd64
go build -o "%OUTPUT_DIR%\security_scan_reader.exe" "%SECURITY_SCAN_READER%"
copy "%CONFIG_FILE%" "%OUTPUT_DIR%" >nul

@echo Build process completed successfully.
