package main

import (
	"bytes"
	"encoding/base64"
	"encoding/csv"
	"encoding/json"
	"flag"
	"fmt"
	"mime/multipart"
	"net/smtp"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Config 配置结构体
type Config struct {
	ScanDir         string     `json:"scan_dir"`         // 扫描目录路径
	FileExt         []string   `json:"file_ext"`         // 文件后缀列表
	EnableArchive   bool       `json:"enable_archive"`   // 是否启用归档功能
	ArchiveDir      string     `json:"archive_dir"`      // 归档目标目录路径
	EnableAntivirus bool       `json:"enable_antivirus"` // 是否启用杀毒扫描
	AntivirusPath   string     `json:"antivirus_path"`   // 杀毒软件可执行文件路径
	Recipients      string     `json:"recipients"`       // 邮件收件人列表
	SMTPConfig      SMTPConfig `json:"smtp_config"`      // SMTP 服务器配置
	ConfigFile      string     `json:"-"`                // 配置文件路径（不序列化）
	OwnerConfig     string     `json:"owner_config"`     // 负责人配置文件路径（预留）
}

// SMTPConfig SMTP 服务器配置
type SMTPConfig struct {
	Host     string `json:"host"`     // SMTP 服务器地址
	Port     int    `json:"port"`     // SMTP 服务器端口
	Username string `json:"username"` // 用户名（可选）
	Password string `json:"password"` // 密码（可选）
	From     string `json:"from"`     // 发件人邮箱地址
	UseTLS   bool   `json:"use_tls"`  // 是否使用 TLS 加密
}

// FileInfo 存储文件信息
type FileInfo struct {
	Filename           string
	Software           string
	Version            string
	FullPath           string
	RelativePath       string
	ModTime            string
	ArchiveStatus      string // 归档状态：成功/失败/跳过
	ResponsiblePerson  string // 负责人
	ResponsibleEmail   string // 负责人邮箱
	SecurityScanResult string // 安全扫描结果
}

// ArchiveStats 归档统计信息
type ArchiveStats struct {
	Total   int64 // 总文件数
	Success int64 // 成功复制的文件数
	Skipped int64 // 跳过的文件数
	Failed  int64 // 失败的文件数
}

func main() {
	fmt.Printf("邮件发送程序\n")
	fmt.Printf("========================================\n")

	// 命令行参数解析
	var (
		configFile   string
		csvFile      string
		recipients   string
		showConfig   bool
		groupedEmail bool
	)

	flag.StringVar(&configFile, "config", "./config.json", "配置文件路径")
	flag.StringVar(&configFile, "c", "./config.json", "配置文件路径的简写形式")
	flag.StringVar(&csvFile, "csv", "exe_files_report_latest.csv", "CSV 报告文件路径")
	flag.StringVar(&recipients, "recipients", "", "邮件收件人，多个收件人用分号分隔")
	flag.StringVar(&recipients, "r", "", "邮件收件人的简写形式")
	flag.BoolVar(&showConfig, "show-config", false, "显示当前配置")
	flag.BoolVar(&groupedEmail, "grouped", false, "启用分组邮件发送")
	flag.BoolVar(&groupedEmail, "g", false, "启用分组邮件发送的简写形式")
	flag.Parse()

	// 加载配置文件
	config, err := loadConfig(configFile)
	if err != nil {
		fmt.Printf("错误：%s\n", err)
		fmt.Printf("提示：请确保配置文件存在，或使用 -config 参数指定正确的配置文件路径\n")
		os.Exit(1)
	}

	// 命令行参数覆盖配置文件参数
	if recipients != "" {
		config.Recipients = recipients
	}

	// 显示配置信息（如果请求）
	if showConfig {
		showEmailConfig(config)
		return
	}

	// 检查 CSV 文件是否存在
	if _, err := os.Stat(csvFile); os.IsNotExist(err) {
		fmt.Printf("错误：CSV 报告文件不存在：%s\n", csvFile)
		fmt.Printf("请先运行 find_exe_files.exe 生成报告文件\n")
		os.Exit(1)
	}

	// 读取 CSV 文件获取文件数量
	fileCount, err := getFileCountFromCSV(csvFile)
	if err != nil {
		fmt.Printf("错误：读取 CSV 文件失败：%s\n", err)
		os.Exit(1)
	}

	fmt.Printf("从 CSV 文件读取到 %d 个文件记录\n", fileCount)

	// 发送邮件
	if groupedEmail {
		err = sendGroupedEmails(config, csvFile, fileCount)
	} else {
		err = sendEmail(config, csvFile, fileCount)
	}

	if err != nil {
		fmt.Printf("邮件发送失败：%s\n", err)
		os.Exit(1)
	}

	fmt.Printf("邮件发送完成\n")
}

// loadConfig 从配置文件加载配置
func loadConfig(configPath string) (*Config, error) {
	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在：%s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("无法读取配置文件：%w", err)
	}

	// 解析 JSON 配置
	config := &Config{}
	if err := json.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("配置文件格式错误：%w", err)
	}

	config.ConfigFile = configPath
	return config, nil
}

// showEmailConfig 显示邮件相关配置
func showEmailConfig(config *Config) {
	fmt.Printf("邮件配置信息：\n")
	fmt.Printf("  配置文件：%s\n", config.ConfigFile)
	fmt.Printf("  邮件收件人：%s\n", config.Recipients)
	fmt.Printf("  SMTP服务器：%s:%d\n", config.SMTPConfig.Host, config.SMTPConfig.Port)
	fmt.Printf("  发件人：%s\n", config.SMTPConfig.From)
	if config.SMTPConfig.UseTLS {
		fmt.Printf("  使用TLS：是\n")
	}
	fmt.Printf("\n")
}

// getFileCountFromCSV 从 CSV 文件获取文件数量
func getFileCountFromCSV(csvFile string) (int, error) {
	file, err := os.Open(csvFile)
	if err != nil {
		return 0, fmt.Errorf("打开 CSV 文件失败：%w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return 0, fmt.Errorf("读取 CSV 文件失败：%w", err)
	}

	// 减去表头行
	if len(records) > 0 {
		return len(records) - 1, nil
	}
	return 0, nil
}

// sendEmail 发送邮件（Go 原生实现）
func sendEmail(config *Config, attachmentFile string, fileCount int) error {
	fmt.Printf("准备发送邮件...\n")

	// 检查收件人是否为空
	if config.Recipients == "" {
		return fmt.Errorf("未指定收件人")
	}

	// 检查附件文件是否存在
	if _, err := os.Stat(attachmentFile); os.IsNotExist(err) {
		return fmt.Errorf("附件文件不存在：%s", attachmentFile)
	}

	// 分割收件人列表
	recipientList := strings.Split(config.Recipients, ";")

	// 清理收件人列表（去除空格）
	for i, recipient := range recipientList {
		recipientList[i] = strings.TrimSpace(recipient)
	}

	// 构建邮件主题（包含日期）
	currentDate := time.Now().Format("2006-01-02")
	subject := fmt.Sprintf(".exe文件搜索结果 - 共找到%d个文件 - %s", fileCount, currentDate)

	// 构建邮件正文
	body := buildEmailBody(fileCount)

	// 发送邮件
	err := sendSMTPEmail(config.SMTPConfig, recipientList, subject, body, attachmentFile)
	if err != nil {
		return fmt.Errorf("SMTP 发送失败：%w", err)
	}

	// 邮件发送成功
	fmt.Printf("邮件已发送至：%s\n", config.Recipients)
	return nil
}

// buildEmailBody 构建邮件正文
func buildEmailBody(fileCount int) string {
	var body bytes.Buffer

	body.WriteString("<html><body>")
	body.WriteString("<h2>.exe文件搜索结果报告</h2>")
	body.WriteString(fmt.Sprintf("<p><strong>搜索完成时间：</strong>%s</p>", time.Now().Format("2006-01-02 15:04:05")))
	body.WriteString(fmt.Sprintf("<p><strong>找到文件总数：</strong>%d 个</p>", fileCount))

	body.WriteString("<p>详细信息请查看附件中的 CSV 报告文件。</p>")
	body.WriteString("<p><em>此邮件由系统自动发送，请勿回复。</em></p>")
	body.WriteString("</body></html>")

	return body.String()
}

// sendSMTPEmail 使用 SMTP 发送邮件
func sendSMTPEmail(smtpConfig SMTPConfig, recipients []string, subject, body, attachmentFile string) error {
	// 构建邮件内容
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 设置邮件头
	buf.WriteString(fmt.Sprintf("From: %s\r\n", smtpConfig.From))
	buf.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(recipients, ", ")))
	buf.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))
	buf.WriteString("MIME-Version: 1.0\r\n")
	buf.WriteString(fmt.Sprintf("Content-Type: multipart/mixed; boundary=%s\r\n", writer.Boundary()))
	buf.WriteString("\r\n")

	// 添加邮件正文
	textPart, err := writer.CreatePart(map[string][]string{
		"Content-Type": {"text/html; charset=utf-8"},
	})
	if err != nil {
		return fmt.Errorf("创建邮件正文部分失败：%w", err)
	}
	textPart.Write([]byte(body))

	// 添加附件
	if attachmentFile != "" {
		attachmentPart, err := writer.CreatePart(map[string][]string{
			"Content-Type":              {"application/octet-stream"},
			"Content-Disposition":       {fmt.Sprintf("attachment; filename=\"%s\"", filepath.Base(attachmentFile))},
			"Content-Transfer-Encoding": {"base64"},
		})
		if err != nil {
			return fmt.Errorf("创建附件部分失败：%w", err)
		}

		// 读取附件文件
		attachmentData, err := os.ReadFile(attachmentFile)
		if err != nil {
			return fmt.Errorf("读取附件文件失败：%w", err)
		}

		// Base64 编码附件
		encoded := base64.StdEncoding.EncodeToString(attachmentData)
		attachmentPart.Write([]byte(encoded))
	}

	writer.Close()

	// 连接 SMTP 服务器
	addr := fmt.Sprintf("%s:%d", smtpConfig.Host, smtpConfig.Port)
	conn, err := smtp.Dial(addr)
	if err != nil {
		return fmt.Errorf("连接 SMTP 服务器失败：%w", err)
	}
	defer conn.Close()

	// 设置发件人
	if err := conn.Mail(smtpConfig.From); err != nil {
		return fmt.Errorf("设置发件人失败：%w", err)
	}

	// 设置收件人
	for _, recipient := range recipients {
		if err := conn.Rcpt(recipient); err != nil {
			return fmt.Errorf("设置收件人 %s 失败：%w", recipient, err)
		}
	}

	// 发送邮件数据
	dataWriter, err := conn.Data()
	if err != nil {
		return fmt.Errorf("开始发送邮件数据失败：%w", err)
	}
	defer dataWriter.Close()

	if _, err := dataWriter.Write(buf.Bytes()); err != nil {
		return fmt.Errorf("写入邮件数据失败：%w", err)
	}

	return nil
}

// sendGroupedEmails 根据负责人分组发送邮件
func sendGroupedEmails(config *Config, csvFile string, fileCount int) error {
	fmt.Printf("准备发送分组邮件...\n")

	// 检查收件人是否为空
	if config.Recipients == "" {
		return fmt.Errorf("未指定收件人")
	}

	// 读取 CSV 文件获取文件信息
	files, err := readCSVFile(csvFile)
	if err != nil {
		return fmt.Errorf("读取 CSV 文件失败：%w", err)
	}

	// 创建邮件分组
	emailGroups := make(map[string][]FileInfo)

	// 将文件按负责人邮箱分组
	for _, fileInfo := range files {
		if fileInfo.ResponsibleEmail != "" {
			emailGroups[fileInfo.ResponsibleEmail] = append(emailGroups[fileInfo.ResponsibleEmail], fileInfo)
		}
	}

	// 如果有负责人邮箱分组，发送分组邮件
	if len(emailGroups) > 0 {
		fmt.Printf("发现 %d 个负责人邮箱分组\n", len(emailGroups))

		for email, groupFiles := range emailGroups {
			fmt.Printf("发送邮件给负责人：%s（%d 个文件）\n", email, len(groupFiles))

			// 创建临时 CSV 文件包含该负责人的文件
			tempCSVFile, err := createTempCSVForGroup(groupFiles, email)
			if err != nil {
				fmt.Printf("创建临时 CSV 文件失败：%s\n", err)
				continue
			}

			// 发送邮件给该负责人
			tempConfig := *config
			tempConfig.Recipients = email
			err = sendEmail(&tempConfig, tempCSVFile, len(groupFiles))
			if err != nil {
				fmt.Printf("发送邮件给 %s 失败：%s\n", email, err)
			}

			// 清理临时文件
			os.Remove(tempCSVFile)
		}
	}

	// 发送汇总邮件给配置中的收件人
	fmt.Printf("发送汇总邮件给配置收件人：%s\n", config.Recipients)
	return sendEmail(config, csvFile, fileCount)
}

// readCSVFile 读取 CSV 文件获取文件信息
func readCSVFile(csvFile string) ([]FileInfo, error) {
	file, err := os.Open(csvFile)
	if err != nil {
		return nil, fmt.Errorf("打开 CSV 文件失败：%w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("读取 CSV 文件失败：%w", err)
	}

	if len(records) == 0 {
		return nil, fmt.Errorf("CSV 文件为空")
	}

	// 解析文件信息
	var files []FileInfo
	for i := 1; i < len(records); i++ { // 跳过表头
		if len(records[i]) >= 9 {
			fileInfo := FileInfo{
				Filename:           records[i][0],
				Software:           records[i][1],
				Version:            records[i][2],
				RelativePath:       records[i][3],
				ModTime:            records[i][4],
				ArchiveStatus:      records[i][5],
				ResponsiblePerson:  records[i][6],
				ResponsibleEmail:   records[i][7],
				SecurityScanResult: records[i][8],
			}
			files = append(files, fileInfo)
		}
	}

	return files, nil
}

// createTempCSVForGroup 为特定负责人创建临时 CSV 文件
func createTempCSVForGroup(files []FileInfo, responsibleEmail string) (string, error) {
	// 创建临时文件名
	tempFileName := fmt.Sprintf("temp_report_%s_%d.csv",
		strings.ReplaceAll(responsibleEmail, "@", "_at_"),
		time.Now().Unix())

	// 创建文件
	file, err := os.Create(tempFileName)
	if err != nil {
		return "", fmt.Errorf("无法创建临时文件: %w", err)
	}
	defer file.Close()

	// 写入 UTF-8 BOM
	_, err = file.Write([]byte{0xEF, 0xBB, 0xBF})
	if err != nil {
		return "", fmt.Errorf("无法写入 BOM: %w", err)
	}

	// 创建 CSV writer
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入表头
	header := []string{"Filename", "Software", "Version", "Full File Path", "Last Modified Time", "Archive Status", "Responsible Person", "Responsible Email", "Security Scan Result"}
	if err := writer.Write(header); err != nil {
		return "", fmt.Errorf("无法写入表头: %w", err)
	}

	// 写入数据行
	for _, fileInfo := range files {
		record := []string{
			fileInfo.Filename,
			fileInfo.Software,
			fileInfo.Version,
			fileInfo.RelativePath,
			fileInfo.ModTime,
			fileInfo.ArchiveStatus,
			fileInfo.ResponsiblePerson,
			fileInfo.ResponsibleEmail,
			fileInfo.SecurityScanResult,
		}
		if err := writer.Write(record); err != nil {
			return "", fmt.Errorf("无法写入数据行: %w", err)
		}
	}

	return tempFileName, nil
}
