package main

import (
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	_ "github.com/mattn/go-sqlite3"
)

// HuorongDetail 火绒扫描结果详情结构
type HuorongDetail struct {
	Detail struct {
		DbVersion    int64  `json:"db_version"`
		Duration     int    `json:"duration"`
		Files        int    `json:"files"`
		Objects      int    `json:"objects"`
		TaskName     string `json:"taskname"`
		ThreatKilled int    `json:"threat_killed"`
		ThreatList   []struct {
			Cat    int    `json:"cat"`
			Clean  int    `json:"clean"`
			Det    string `json:"det"`
			Fn     string `json:"fn"` // 文件路径
			ID     int    `json:"id"`
			Mcs    int    `json:"mcs"`
			Md5    string `json:"md5"`
			ObjN   string `json:"objn"`
			Rid    int64  `json:"rid"`
			Sha1   string `json:"sha1"`
			Sha256 string `json:"sha256"`
			Solid  int    `json:"solid"`
		} `json:"threat_list"`
		Threats int   `json:"threats"`
		TmStart int64 `json:"tm_start"`
	} `json:"detail"`
	Fid     int   `json:"fid"`
	Guid    int64 `json:"guid"`
	Version struct {
		DbTime  int64  `json:"dbtime"`
		Product string `json:"product"`
	} `json:"version"`
}

// CSVRecord CSV 记录结构
type CSVRecord struct {
	Filename           string
	Software           string
	Version            string
	FullFilePath       string
	LastModifiedTime   string
	ArchiveStatus      string
	ResponsiblePerson  string
	ResponsibleEmail   string
	SecurityScanResult string
}

func main() {
	fmt.Printf("火绒安全扫描结果读取器\n")
	fmt.Printf("========================================\n")

	// 检查输入文件是否存在
	inputFile := "exe_files_report_latest.csv"
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		fmt.Printf("错误：输入文件不存在：%s\n", inputFile)
		fmt.Printf("请先运行 find_exe_files.exe 生成报告文件\n")
		os.Exit(1)
	}

	// 读取火绒数据库
	threatFiles, err := readHuorongDatabase()
	if err != nil {
		fmt.Printf("读取火绒数据库失败：%s\n", err)
		os.Exit(1)
	}

	// 更新 CSV 文件
	err = updateCSVWithSecurityResults(inputFile, threatFiles)
	if err != nil {
		fmt.Printf("更新 CSV 文件失败：%s\n", err)
		os.Exit(1)
	}

	fmt.Printf("安全扫描结果更新完成\n")
}

// readHuorongDatabase 读取火绒数据库获取威胁文件列表
func readHuorongDatabase() (map[string]bool, error) {
	fmt.Printf("正在读取火绒安全数据库...\n")

	// 火绒数据库路径
	dbPath := `C:\ProgramData\Huorong\Sysdiag\log.db`

	// 检查数据库文件是否存在
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("火绒数据库文件不存在：%s", dbPath)
	}

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "huorong_scan_*")
	if err != nil {
		return nil, fmt.Errorf("创建临时目录失败：%w", err)
	}
	defer os.RemoveAll(tempDir) // 清理临时目录

	// 复制数据库文件到临时目录
	tempDbPath := filepath.Join(tempDir, "log.db")
	err = copyFile(dbPath, tempDbPath)
	if err != nil {
		return nil, fmt.Errorf("复制数据库文件失败：%w", err)
	}

	// 复制相关的 WAL 和 SHM 文件（如果存在）
	walPath := dbPath + "-wal"
	shmPath := dbPath + "-shm"

	if _, err := os.Stat(walPath); err == nil {
		copyFile(walPath, tempDbPath+"-wal")
	}
	if _, err := os.Stat(shmPath); err == nil {
		copyFile(shmPath, tempDbPath+"-shm")
	}

	// 打开数据库
	db, err := sql.Open("sqlite3", tempDbPath)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败：%w", err)
	}
	defer db.Close()

	// 查询最新的扫描记录
	query := `SELECT detail FROM HrLogV3_60 ORDER BY ts DESC LIMIT 1`
	var detailJSON string
	err = db.QueryRow(query).Scan(&detailJSON)
	if err != nil {
		if err == sql.ErrNoRows {
			fmt.Printf("警告：未找到扫描记录\n")
			return make(map[string]bool), nil
		}
		return nil, fmt.Errorf("查询数据库失败：%w", err)
	}

	// 解析 JSON 数据
	var detail HuorongDetail
	err = json.Unmarshal([]byte(detailJSON), &detail)
	if err != nil {
		return nil, fmt.Errorf("解析 JSON 数据失败：%w", err)
	}

	// 提取威胁文件路径
	threatFiles := make(map[string]bool)
	for _, threat := range detail.Detail.ThreatList {
		if threat.Fn != "" {
			// 标准化路径（转换为小写，统一路径分隔符）
			normalizedPath := strings.ToLower(strings.ReplaceAll(threat.Fn, "\\", "/"))
			threatFiles[normalizedPath] = true
			fmt.Printf("发现威胁文件：%s (检测：%s)\n", threat.Fn, threat.Det)
		}
	}

	fmt.Printf("共发现 %d 个威胁文件\n", len(threatFiles))
	return threatFiles, nil
}

// isFileInThreatList 检查文件是否在威胁列表中
// 使用智能匹配算法，支持绝对路径与相对路径的关联
func isFileInThreatList(filename, software, version, csvPath string, threatFiles map[string]bool) bool {
	fmt.Printf("\n=== 开始匹配文件：%s ===\n", filename)
	fmt.Printf("CSV路径：%s\n", csvPath)
	fmt.Printf("软件名：%s，版本：%s\n", software, version)

	// 标准化 CSV 路径
	csvPathNormalized := strings.ToLower(strings.ReplaceAll(csvPath, "\\", "/"))
	filenameLower := strings.ToLower(filename)

	// 方法1：精确路径后缀匹配（优先级最高）
	fmt.Printf("\n--- 方法1：精确路径后缀匹配 ---\n")
	for threatPath := range threatFiles {
		threatPathLower := strings.ToLower(strings.ReplaceAll(threatPath, "\\", "/"))

		// 检查威胁路径是否以 CSV 相对路径结尾
		if strings.HasSuffix(threatPathLower, csvPathNormalized) {
			fmt.Printf("✅ 匹配成功（精确后缀）：%s\n", threatPath)
			fmt.Printf("   威胁路径：%s\n", threatPathLower)
			fmt.Printf("   CSV路径：%s\n", csvPathNormalized)
			return true
		}
	}

	// 方法2：路径分解匹配
	fmt.Printf("\n--- 方法2：路径分解匹配 ---\n")
	csvPathParts := strings.Split(csvPathNormalized, "/")
	fmt.Printf("CSV路径分解：%v\n", csvPathParts)

	for threatPath := range threatFiles {
		threatPathLower := strings.ToLower(strings.ReplaceAll(threatPath, "\\", "/"))
		threatPathParts := strings.Split(threatPathLower, "/")

		// 检查威胁路径的后缀部分是否与 CSV 路径匹配
		if len(threatPathParts) >= len(csvPathParts) {
			// 提取威胁路径的后缀部分
			startIndex := len(threatPathParts) - len(csvPathParts)
			threatSuffix := threatPathParts[startIndex:]

			// 逐个比较路径部分
			allMatch := true
			for i, csvPart := range csvPathParts {
				if csvPart != "" && csvPart != threatSuffix[i] {
					allMatch = false
					break
				}
			}

			if allMatch {
				fmt.Printf("✅ 匹配成功（路径分解）：%s\n", threatPath)
				fmt.Printf("   威胁路径后缀：%v\n", threatSuffix)
				fmt.Printf("   CSV路径分解：%v\n", csvPathParts)
				return true
			}
		}
	}

	// 方法3：文件名 + 目录结构匹配
	fmt.Printf("\n--- 方法3：文件名 + 目录结构匹配 ---\n")
	for threatPath := range threatFiles {
		threatPathLower := strings.ToLower(strings.ReplaceAll(threatPath, "\\", "/"))

		// 检查是否以相同文件名结尾
		if strings.HasSuffix(threatPathLower, filenameLower) {
			// 检查路径中是否包含 CSV 路径的关键部分
			csvPathParts := strings.Split(csvPathNormalized, "/")
			matchCount := 0

			for _, part := range csvPathParts {
				if part != "" && part != filenameLower && strings.Contains(threatPathLower, part) {
					matchCount++
				}
			}

			// 如果大部分路径部分都匹配，认为是同一个文件
			if matchCount > 0 && matchCount >= len(csvPathParts)-2 { // 允许1-2个部分不匹配
				fmt.Printf("✅ 匹配成功（文件名+结构）：%s\n", threatPath)
				fmt.Printf("   匹配的路径部分数：%d/%d\n", matchCount, len(csvPathParts)-1)
				return true
			}
		}
	}

	// 方法4：软件名 + 版本号 + 文件名匹配
	fmt.Printf("\n--- 方法4：软件名 + 版本号 + 文件名匹配 ---\n")
	if software != "-" && software != "" {
		// 构建可能的目录名模式
		possiblePatterns := []string{
			software,                               // "FIST SMS"
			strings.ReplaceAll(software, " ", "_"), // "FIST_SMS"
			strings.ReplaceAll(software, " ", ""),  // "FISTSMS"
			strings.ReplaceAll(software, " ", "-"), // "FIST-SMS"
		}

		if version != "-" && version != "" {
			// 添加包含版本号的模式
			basePatterns := make([]string, len(possiblePatterns))
			copy(basePatterns, possiblePatterns)

			for _, pattern := range basePatterns {
				possiblePatterns = append(possiblePatterns,
					pattern+" "+version, // "FIST SMS V100R002B02D040"
					pattern+"_"+version, // "FIST_SMS_V100R002B02D040"
					pattern+version,     // "FISTSMSV100R002B02D040"
					pattern+"-"+version, // "FIST-SMS-V100R002B02D040"
				)
			}
		}

		fmt.Printf("可能的软件名模式：%v\n", possiblePatterns)

		for threatPath := range threatFiles {
			threatPathLower := strings.ToLower(strings.ReplaceAll(threatPath, "\\", "/"))

			// 检查是否以相同文件名结尾
			if strings.HasSuffix(threatPathLower, filenameLower) {
				// 检查路径中是否包含软件名模式
				for _, pattern := range possiblePatterns {
					patternLower := strings.ToLower(pattern)
					if strings.Contains(threatPathLower, patternLower) {
						fmt.Printf("✅ 匹配成功（软件名模式）：%s\n", threatPath)
						fmt.Printf("   匹配的模式：%s\n", pattern)
						return true
					}
				}
			}
		}
	}

	fmt.Printf("❌ 未找到匹配的威胁文件\n")
	return false
}

// updateCSVWithSecurityResults 更新 CSV 文件的安全扫描结果
func updateCSVWithSecurityResults(csvFile string, threatFiles map[string]bool) error {
	fmt.Printf("正在更新 CSV 文件的安全扫描结果...\n")

	// 读取 CSV 文件
	file, err := os.Open(csvFile)
	if err != nil {
		return fmt.Errorf("打开 CSV 文件失败：%w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("读取 CSV 文件失败：%w", err)
	}

	if len(records) == 0 {
		return fmt.Errorf("CSV 文件为空")
	}

	// 检查表头格式
	header := records[0]
	if len(header) < 9 || header[8] != "Security Scan Result" {
		return fmt.Errorf("CSV 文件格式不正确，缺少 Security Scan Result 字段")
	}

	// 更新安全扫描结果
	threatCount := 0
	for i := 1; i < len(records); i++ {
		if len(records[i]) >= 9 {
			// 获取 CSV 文件中的信息
			filename := records[i][0] // Filename 字段
			software := records[i][1] // Software 字段
			version := records[i][2]  // Version 字段
			filePath := records[i][3] // Full File Path 字段（相对路径）

			// 检查是否匹配威胁文件
			if isFileInThreatList(filename, software, version, filePath, threatFiles) {
				records[i][8] = "威胁检测"
				threatCount++
			} else {
				// 未匹配到威胁的文件，Security Scan Result 字段保持为空
				records[i][8] = ""
			}
		}
	}

	// 写回 CSV 文件
	outputFile, err := os.Create(csvFile)
	if err != nil {
		return fmt.Errorf("创建输出文件失败：%w", err)
	}
	defer outputFile.Close()

	// 写入 UTF-8 BOM
	_, err = outputFile.Write([]byte{0xEF, 0xBB, 0xBF})
	if err != nil {
		return fmt.Errorf("写入 BOM 失败：%w", err)
	}

	writer := csv.NewWriter(outputFile)
	defer writer.Flush()

	err = writer.WriteAll(records)
	if err != nil {
		return fmt.Errorf("写入 CSV 文件失败：%w", err)
	}

	fmt.Printf("已更新 %d 个文件的安全扫描结果\n", len(records)-1)
	fmt.Printf("其中 %d 个文件被标记为威胁检测\n", threatCount)
	return nil
}

// copyFile 复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}
