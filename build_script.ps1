# PowerShell script to compile Go programs and organize build output

# Define paths
$RootDir = "c:\\Users\\<USER>\\Project\\find_exe"
$BuildDir = "$RootDir\\build"
$ConfigFile = "$RootDir\\config.json"

# Define programs and their directories
$Programs = @{
    "email_sender" = "$RootDir\\email_sender\\email_sender.go"
    "find_exe_files" = "$RootDir\\find_exe_files\\find_exe_files.go"
    "security_scan_reader" = "$RootDir\\security_scan_reader\\security_scan_reader.go"
}

# Ensure build directory exists
if (-Not (Test-Path -Path $BuildDir)) {
    New-Item -ItemType Directory -Path $BuildDir | Out-Null
}

# Compile each program
foreach ($Program in $Programs.GetEnumerator()) {
    $ProgramName = $Program.Key
    $ProgramPath = $Program.Value
    $OutputDir = "$BuildDir\\$ProgramName"

    # Ensure output directory exists
    if (-Not (Test-Path -Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir | Out-Null
    }

    # Compile the program
    go build -o "$OutputDir\\$ProgramName.exe" $ProgramPath

    # Copy config.json to the output directory
    Copy-Item -Path $ConfigFile -Destination $OutputDir -Force
}

Write-Host "Build process completed successfully."
