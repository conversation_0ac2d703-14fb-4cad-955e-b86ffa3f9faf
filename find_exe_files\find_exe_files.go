package main

import (
	"crypto/md5"
	"encoding/csv"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// Config 配置结构体
type Config struct {
	ScanDir         string     `json:"scan_dir"`         // 扫描目录路径
	FileExt         []string   `json:"file_ext"`         // 文件后缀列表
	EnableArchive   bool       `json:"enable_archive"`   // 是否启用归档功能
	ArchiveDir      string     `json:"archive_dir"`      // 归档目标目录路径
	EnableAntivirus bool       `json:"enable_antivirus"` // 是否启用杀毒扫描
	AntivirusPath   string     `json:"antivirus_path"`   // 杀毒软件可执行文件路径
	Recipients      string     `json:"recipients"`       // 邮件收件人列表
	SMTPConfig      SMTPConfig `json:"smtp_config"`      // SMTP 服务器配置
	ConfigFile      string     `json:"-"`                // 配置文件路径（不序列化）
	OwnerConfig     string     `json:"owner_config"`     // 负责人配置文件路径（预留）
	WorkerCount     int        `json:"worker_count"`     // 并发工作线程数
}

// SMTPConfig SMTP 服务器配置
type SMTPConfig struct {
	Host     string `json:"host"`     // SMTP 服务器地址
	Port     int    `json:"port"`     // SMTP 服务器端口
	Username string `json:"username"` // 用户名（可选）
	Password string `json:"password"` // 密码（可选）
	From     string `json:"from"`     // 发件人邮箱地址
	UseTLS   bool   `json:"use_tls"`  // 是否使用 TLS 加密
}

// FileInfo 存储文件信息
type FileInfo struct {
	Filename           string
	Software           string
	Version            string
	FullPath           string
	RelativePath       string
	ModTime            string
	ArchiveStatus      string // 归档状态：成功/失败/跳过
	ResponsiblePerson  string // 负责人
	ResponsibleEmail   string // 负责人邮箱
	SecurityScanResult string // 安全扫描结果
}

// OwnerInfo 负责人信息
type OwnerInfo struct {
	SoftwarePattern   string // 软件名称模式
	ResponsiblePerson string // 负责人
	Email             string // 邮箱地址
}

// 全局变量用于存储找到的文件
var (
	foundFiles    []FileInfo
	mutex         sync.Mutex
	wg            sync.WaitGroup
	archiveDir    string       // 归档目录
	searchBaseDir string       // 搜索基础目录
	archiveStats  ArchiveStats // 归档统计信息
	ownerInfos    []OwnerInfo  // 负责人信息列表
)

// ArchiveStats 归档统计信息
type ArchiveStats struct {
	Total   int64 // 总文件数
	Success int64 // 成功复制数
	Failed  int64 // 失败复制数
	Skipped int64 // 跳过复制数
}

// getDefaultConfig 返回默认配置
func getDefaultConfig() *Config {
	return &Config{
		ScanDir:         ".",
		FileExt:         []string{".exe"},
		EnableArchive:   false,
		ArchiveDir:      "",
		EnableAntivirus: false,
		AntivirusPath:   "",
		Recipients:      "",
		SMTPConfig: SMTPConfig{
			Host:     "************",
			Port:     25,
			Username: "",
			Password: "",
			From:     "<EMAIL>",
			UseTLS:   false,
		},
		ConfigFile:  "./config.json",
		OwnerConfig: "",
		WorkerCount: 1000, // 默认工作线程数
	}
}

// loadConfig 从配置文件加载配置
func loadConfig(configPath string) (*Config, error) {
	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在：%s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("无法读取配置文件：%w", err)
	}

	// 解析 JSON 配置
	config := getDefaultConfig()
	if err := json.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("配置文件格式错误：%w", err)
	}

	config.ConfigFile = configPath
	return config, nil
}

// saveConfig 保存配置到文件
func saveConfig(config *Config, configPath string) error {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("无法序列化配置：%w", err)
	}

	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("无法写入配置文件：%w", err)
	}

	return nil
}

// validateConfig 验证配置参数
func validateConfig(config *Config) error {
	// 验证扫描目录
	if config.ScanDir == "" {
		return fmt.Errorf("扫描目录不能为空")
	}

	// 验证文件后缀
	if len(config.FileExt) == 0 {
		return fmt.Errorf("文件后缀列表不能为空")
	}

	// 验证归档配置
	if config.EnableArchive && config.ArchiveDir == "" {
		return fmt.Errorf("启用归档功能时必须指定归档目录")
	}

	// 验证杀毒软件配置
	if config.EnableAntivirus {
		if config.AntivirusPath == "" {
			return fmt.Errorf("启用杀毒扫描时必须指定杀毒软件路径")
		}
		if !config.EnableArchive {
			return fmt.Errorf("杀毒扫描功能需要同时启用归档功能")
		}
	}

	return nil
}

// showConfig 显示当前配置
func showConfig(config *Config) {
	logger.Printf("当前配置信息：\n")
	logger.Printf("  配置文件：%s\n", config.ConfigFile)
	logger.Printf("  扫描目录：%s\n", config.ScanDir)
	logger.Printf("  文件后缀：%v\n", config.FileExt)
	logger.Printf("  启用归档：%t\n", config.EnableArchive)
	if config.EnableArchive {
		logger.Printf("  归档目录：%s\n", config.ArchiveDir)
	}
	logger.Printf("  启用杀毒：%t\n", config.EnableAntivirus)
	if config.EnableAntivirus {
		logger.Printf("  杀毒软件：%s\n", config.AntivirusPath)
	}
	if config.OwnerConfig != "" {
		logger.Printf("  负责人配置：%s\n", config.OwnerConfig)
	}
	logger.Printf("  工作线程数：%d\n", config.WorkerCount)
	logger.Printf("\n")
}

// loadOwnerConfig 加载负责人配置文件
func loadOwnerConfig(ownerConfigPath string) error {
	// 如果未指定配置文件，跳过加载
	if ownerConfigPath == "" {
		logger.Printf("未指定负责人配置文件，负责人信息将为空\n")
		return nil
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(ownerConfigPath); os.IsNotExist(err) {
		return fmt.Errorf("负责人配置文件不存在：%s", ownerConfigPath)
	}

	// 打开 CSV 文件
	file, err := os.Open(ownerConfigPath)
	if err != nil {
		return fmt.Errorf("无法打开负责人配置文件：%w", err)
	}
	defer file.Close()

	// 创建 CSV 读取器
	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("读取负责人配置文件失败：%w", err)
	}

	// 清空现有的负责人信息
	ownerInfos = []OwnerInfo{}

	// 解析 CSV 记录（跳过标题行）
	for i, record := range records {
		if i == 0 {
			// 验证标题行格式（去除空格）
			if len(record) < 3 ||
				strings.TrimSpace(record[0]) != "software_pattern" ||
				strings.TrimSpace(record[1]) != "responsible_person" ||
				strings.TrimSpace(record[2]) != "email" {
				return fmt.Errorf("负责人配置文件格式错误：标题行应为 'software_pattern,responsible_person,email'，实际为：%v", record)
			}
			continue
		}

		if len(record) < 3 {
			logger.Printf("警告：负责人配置文件第 %d 行格式不正确，跳过\n", i+1)
			continue
		}

		ownerInfo := OwnerInfo{
			SoftwarePattern:   strings.TrimSpace(record[0]),
			ResponsiblePerson: strings.TrimSpace(record[1]),
			Email:             strings.TrimSpace(record[2]),
		}

		ownerInfos = append(ownerInfos, ownerInfo)
	}

	logger.Printf("成功加载负责人配置：%d 条记录\n", len(ownerInfos))
	return nil
}

// findResponsiblePerson 根据软件名称查找负责人信息
func findResponsiblePerson(software string) (string, string) {
	if software == "" {
		return "", ""
	}

	// 转换为小写进行匹配
	softwareLower := strings.ToLower(software)

	// 遍历负责人信息列表
	for _, owner := range ownerInfos {
		if owner.SoftwarePattern == "" {
			continue
		}

		// 使用字符串包含匹配（不区分大小写）
		patternLower := strings.ToLower(owner.SoftwarePattern)
		if strings.Contains(softwareLower, patternLower) {
			return owner.ResponsiblePerson, owner.Email
		}
	}

	// 未找到匹配的负责人
	return "", ""
}

var logger *log.Logger

func initLogger() {

	// Open the log file
	logFile, err := os.OpenFile("app.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Printf("无法创建日志文件: %v\n", err)
		os.Exit(1)
	}

	// Create a multi-writer to write to both stdout and the log file
	multiWriter := io.MultiWriter(os.Stdout, logFile)

	// Configure the logger
	logger = log.New(multiWriter, "[find_exe_files] ", log.LstdFlags|log.Lshortfile)
}

func main() {
	initLogger()
	logger.Println("日志功能初始化完成")

	// 定义命令行参数变量
	var (
		scanDir         string
		fileExt         string
		enableArchive   bool
		archiveDirFlag  string
		enableAntivirus bool
		antivirusPath   string
		recipients      string
		configFile      string
		ownerConfig     string
		showConfigFlag  bool
	)

	// 命令行参数解析
	flag.StringVar(&scanDir, "scan-dir", "", "扫描目录路径")
	flag.StringVar(&scanDir, "d", "", "扫描目录路径的简写形式")
	flag.StringVar(&fileExt, "file-ext", "", "文件后缀，多个后缀用逗号分隔（如 .exe,.dll,.sys）")
	flag.StringVar(&fileExt, "e", "", "文件后缀的简写形式")
	flag.BoolVar(&enableArchive, "enable-archive", false, "是否启用归档功能")
	flag.BoolVar(&enableArchive, "archive", false, "是否启用归档功能的简写形式")
	flag.StringVar(&archiveDirFlag, "archive-dir", "", "归档目标目录路径")
	flag.StringVar(&archiveDirFlag, "a", "", "归档目标目录路径的简写形式")
	flag.BoolVar(&enableAntivirus, "enable-antivirus", false, "是否启用杀毒扫描")
	flag.BoolVar(&enableAntivirus, "antivirus", false, "是否启用杀毒扫描的简写形式")
	flag.StringVar(&antivirusPath, "antivirus-path", "", "杀毒软件可执行文件路径")
	flag.StringVar(&antivirusPath, "av", "", "杀毒软件可执行文件路径的简写形式")
	flag.StringVar(&recipients, "recipients", "", "邮件收件人，多个收件人用分号分隔")
	flag.StringVar(&recipients, "r", "", "邮件收件人的简写形式")
	flag.StringVar(&configFile, "config", "./config.json", "配置文件路径")
	flag.StringVar(&configFile, "c", "./config.json", "配置文件路径的简写形式")
	flag.StringVar(&ownerConfig, "owner-config", "", "负责人配置文件路径（预留参数）")
	flag.BoolVar(&showConfigFlag, "show-config", false, "显示当前有效配置")
	flag.Parse()

	// 加载配置文件
	config, err := loadConfig(configFile)
	if err != nil {
		logger.Printf("错误：%s\n", err)
		logger.Printf("提示：请确保配置文件存在，或使用 -config 参数指定正确的配置文件路径\n")
		os.Exit(1)
	}

	// 命令行参数覆盖配置文件参数
	if scanDir != "" {
		config.ScanDir = scanDir
	}
	if fileExt != "" {
		config.FileExt = strings.Split(fileExt, ",")
		// 清理空格并确保以点开头
		for i, ext := range config.FileExt {
			ext = strings.TrimSpace(ext)
			if !strings.HasPrefix(ext, ".") {
				ext = "." + ext
			}
			config.FileExt[i] = ext
		}
	}
	if enableArchive {
		config.EnableArchive = true
	}
	if archiveDirFlag != "" {
		config.ArchiveDir = archiveDirFlag
		config.EnableArchive = true // 指定归档目录时自动启用归档
	}
	if enableAntivirus {
		config.EnableAntivirus = true
	}
	if antivirusPath != "" {
		config.AntivirusPath = antivirusPath
		config.EnableAntivirus = true // 指定杀毒软件路径时自动启用杀毒
	}
	if recipients != "" {
		config.Recipients = recipients
	}
	if ownerConfig != "" {
		config.OwnerConfig = ownerConfig
	}

	// 如果有位置参数，使用第一个作为扫描目录
	if flag.NArg() > 0 {
		config.ScanDir = flag.Arg(0)
	}

	// 显示配置信息（如果请求）
	if showConfigFlag {
		showConfig(config)
		return
	}

	// 验证配置
	if err := validateConfig(config); err != nil {
		logger.Printf("配置错误：%s\n", err)
		os.Exit(1)
	}

	// 加载负责人配置
	if err := loadOwnerConfig(config.OwnerConfig); err != nil {
		logger.Printf("负责人配置加载失败：%s\n", err)
		os.Exit(1)
	}

	// 获取绝对路径
	absDir, err := filepath.Abs(config.ScanDir)
	if err != nil {
		logger.Printf("错误：无法获取目录的绝对路径：%s\n", err)
		os.Exit(1)
	}

	// 检查目录是否存在
	if _, err := os.Stat(absDir); os.IsNotExist(err) {
		logger.Printf("错误：目录不存在或无法访问：%s\n", absDir)
		os.Exit(1)
	}

	// 设置全局变量
	searchBaseDir = absDir

	// 处理归档目录
	if config.EnableArchive {
		archiveDir, err = filepath.Abs(config.ArchiveDir)
		if err != nil {
			logger.Printf("错误：无法获取归档目录的绝对路径：%s\n", err)
			os.Exit(1)
		}

		// 创建归档根目录（如果不存在）
		if err := os.MkdirAll(archiveDir, 0755); err != nil {
			logger.Printf("错误：无法创建归档目录：%s\n", err)
			os.Exit(1)
		}

		logger.Printf("归档目录：%s\n", archiveDir)
	}

	// 处理杀毒软件参数
	if config.EnableAntivirus {
		// 验证杀毒软件路径是否存在
		if _, err := os.Stat(config.AntivirusPath); os.IsNotExist(err) {
			logger.Printf("错误：杀毒软件路径不存在：%s\n", config.AntivirusPath)
			os.Exit(1)
		}
		logger.Printf("杀毒软件：%s\n", config.AntivirusPath)
	}

	// 生成带时间戳的输出文件名
	currentTime := time.Now().Format("20060102_150405")
	outputFile := fmt.Sprintf("exe_files_report_%s.csv", currentTime)

	logger.Printf("开始搜索目录：%s\n", absDir)

	// 递归搜索文件
	err = searchFiles(absDir, config.FileExt, config.WorkerCount)
	if err != nil {
		logger.Printf("搜索文件时出错：%s\n", err)
		os.Exit(1)
	}

	// 创建 CSV 文件
	err = createCSVReport(outputFile)
	if err != nil {
		logger.Printf("创建 CSV 文件时出错：%s\n", err)
		os.Exit(1)
	}

	// 创建最新报告副本
	latestFile := "exe_files_report_latest.csv"
	err = copyFile(outputFile, latestFile)
	if err != nil {
		logger.Printf("警告：创建最新报告副本失败：%s\n", err)
	} else {
		logger.Printf("最新报告副本已创建：%s\n", latestFile)
	}

	logger.Printf("搜索完成，结果已保存到：%s\n", outputFile)
	logger.Printf("共找到 %d 个 .exe 文件\n", len(foundFiles))

	// 显示归档统计信息
	if archiveDir != "" {
		logger.Printf("\n归档统计信息：\n")
		logger.Printf("  总文件数：%d\n", archiveStats.Total)
		logger.Printf("  成功复制：%d\n", archiveStats.Success)
		logger.Printf("  跳过复制：%d\n", archiveStats.Skipped)
		logger.Printf("  复制失败：%d\n", archiveStats.Failed)
		if archiveStats.Total > 0 {
			successRate := float64(archiveStats.Success) / float64(archiveStats.Total) * 100
			logger.Printf("  成功率：%.1f%%\n", successRate)
		}
	}

	// 邮件发送功能已移至独立的 email_sender.exe 程序
	// 如需发送邮件，请运行：email_sender.exe -csv "报告文件名.csv"

	// 如果提供了杀毒软件参数且使用了归档功能，启动杀毒扫描
	if config.EnableAntivirus && config.EnableArchive && archiveStats.Success > 0 {
		startAntivirusScan(config.AntivirusPath, archiveDir)
	}
}

// searchFiles 递归搜索指定扩展名的文件
func searchFiles(searchDir string, fileExtensions []string, work) error {
	fileQueue := make(chan string, 5000) // Buffered channel for file paths
	var wg sync.WaitGroup

	// Start worker pool
	workerCount := 100
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for filePath := range fileQueue {
				processExeFile(filePath)
			}
		}()
	}

	err := filepath.Walk(searchDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			logger.Printf("警告：无法访问 %s: %s\n", path, err)
			return nil
		}

		if !info.IsDir() {
			fileName := strings.ToLower(info.Name())
			for _, ext := range fileExtensions {
				if strings.HasSuffix(fileName, strings.ToLower(ext)) {
					fileQueue <- path // Send file path to the queue
					break
				}
			}
		}
		return nil
	})

	close(fileQueue) // Close the channel after Walk completes
	wg.Wait()        // Wait for all workers to finish
	return err
}

// processExeFile 处理单个 .exe 文件
func processExeFile(filePath string) {
	info, err := os.Stat(filePath)
	if err != nil {
		logger.Printf("警告：无法获取文件信息 %s: %s\n", filePath, err)
		return
	}

	filename := info.Name()

	// 计算相对路径（相对于搜索基础目录）
	relativePath, err := filepath.Rel(searchBaseDir, filePath)
	if err != nil {
		relativePath = filePath
	}

	// 格式化修改时间
	modTime := info.ModTime().Format("2006-01-02 15:04:05")

	// 提取软件名和版本信息
	software, version := extractSoftwareInfo(relativePath)

	// 查找负责人信息
	responsiblePerson, responsibleEmail := findResponsiblePerson(software)

	// 执行归档操作
	archiveStatus := "跳过"
	if archiveDir != "" {
		archiveStatus = archiveFile(filePath, relativePath)
	}

	fileInfo := FileInfo{
		Filename:           filename,
		Software:           software,
		Version:            version,
		FullPath:           filePath,
		RelativePath:       relativePath,
		ModTime:            modTime,
		ArchiveStatus:      archiveStatus,
		ResponsiblePerson:  responsiblePerson,
		ResponsibleEmail:   responsibleEmail,
		SecurityScanResult: "", // 默认为空，由安全扫描脚本更新
	}

	// 线程安全地添加到结果列表
	mutex.Lock()
	foundFiles = append(foundFiles, fileInfo)
	mutex.Unlock()
}

// archiveFile 归档单个文件（增强版本，支持文件比较和覆盖）
func archiveFile(sourcePath, relativePath string) string {
	// 更新总文件数统计
	atomic.AddInt64(&archiveStats.Total, 1)

	// 计算目标路径
	targetPath := filepath.Join(archiveDir, relativePath)
	targetDir := filepath.Dir(targetPath)

	// 创建目标目录
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		atomic.AddInt64(&archiveStats.Failed, 1)
		return fmt.Sprintf("失败: 创建目录失败 - %s", err.Error())
	}

	// 获取源文件信息
	sourceInfo, err := os.Stat(sourcePath)
	if err != nil {
		atomic.AddInt64(&archiveStats.Failed, 1)
		return fmt.Sprintf("失败: 无法获取源文件信息 - %s", err.Error())
	}

	// 检查目标文件是否已存在
	targetInfo, err := os.Stat(targetPath)
	if err == nil {
		// 目标文件存在，进行比较
		compareResult := compareFiles(sourcePath, targetPath, sourceInfo, targetInfo)
		switch compareResult {
		case "same":
			atomic.AddInt64(&archiveStats.Skipped, 1)
			return "跳过-相同"
		case "newer":
			// 源文件更新，覆盖目标文件
			if err := copyFile(sourcePath, targetPath); err != nil {
				atomic.AddInt64(&archiveStats.Failed, 1)
				return fmt.Sprintf("失败: %s", err.Error())
			}
			atomic.AddInt64(&archiveStats.Success, 1)
			return "覆盖-更新"
		case "different":
			// 内容不同，覆盖目标文件
			if err := copyFile(sourcePath, targetPath); err != nil {
				atomic.AddInt64(&archiveStats.Failed, 1)
				return fmt.Sprintf("失败: %s", err.Error())
			}
			atomic.AddInt64(&archiveStats.Success, 1)
			return "覆盖-内容不同"
		default:
			atomic.AddInt64(&archiveStats.Skipped, 1)
			return "跳过-未知原因"
		}
	} else if os.IsNotExist(err) {
		// 目标文件不存在，直接复制
		if err := copyFile(sourcePath, targetPath); err != nil {
			atomic.AddInt64(&archiveStats.Failed, 1)
			return fmt.Sprintf("失败: %s", err.Error())
		}
		atomic.AddInt64(&archiveStats.Success, 1)
		return "成功"
	} else {
		// 其他错误
		atomic.AddInt64(&archiveStats.Failed, 1)
		return fmt.Sprintf("失败: 无法检查目标文件 - %s", err.Error())
	}
}

// compareFiles 比较两个文件，返回比较结果
func compareFiles(sourcePath, targetPath string, sourceInfo, targetInfo os.FileInfo) string {
	// 首先比较修改时间
	sourceModTime := sourceInfo.ModTime()
	targetModTime := targetInfo.ModTime()

	if sourceModTime.After(targetModTime) {
		// 源文件更新
		return "newer"
	} else if sourceModTime.Before(targetModTime) {
		// 源文件较旧，跳过
		return "same"
	} else {
		// 修改时间相同，比较文件内容（使用 MD5）
		sourceMD5, err := calculateFileMD5(sourcePath)
		if err != nil {
			// MD5 计算失败，假设不同
			return "different"
		}

		targetMD5, err := calculateFileMD5(targetPath)
		if err != nil {
			// MD5 计算失败，假设不同
			return "different"
		}

		if sourceMD5 == targetMD5 {
			return "same"
		} else {
			return "different"
		}
	}
}

// calculateFileMD5 计算文件的 MD5 校验和
func calculateFileMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// copyFile 复制文件，支持跨文件系统
func copyFile(src, dst string) error {
	// 打开源文件
	sourceFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("无法打开源文件: %w", err)
	}
	defer sourceFile.Close()

	// 创建目标文件
	destFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("无法创建目标文件: %w", err)
	}
	defer destFile.Close()

	// 复制文件内容
	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return fmt.Errorf("复制文件内容失败: %w", err)
	}

	// 同步到磁盘
	err = destFile.Sync()
	if err != nil {
		return fmt.Errorf("同步文件到磁盘失败: %w", err)
	}

	// 复制文件权限和时间戳
	sourceInfo, err := sourceFile.Stat()
	if err == nil {
		os.Chmod(dst, sourceInfo.Mode())
		os.Chtimes(dst, sourceInfo.ModTime(), sourceInfo.ModTime())
	}

	return nil
}

// extractSoftwareInfo 从路径中提取软件名和版本信息
func extractSoftwareInfo(path string) (software, version string) {
	software = "-"
	version = "-"

	// 将路径分割为段
	pathSegments := regexp.MustCompile(`[\\/]`).Split(path, -1)

	// 查找版本模式 (V\d{3}R\d{3})
	versionPattern := regexp.MustCompile(`V\d{3}R\d{3}`)

	for _, segment := range pathSegments {
		if versionPattern.MatchString(segment) {
			// 使用空格、下划线、连字符分割段落
			parts := regexp.MustCompile(`[ _-]`).Split(segment, -1)

			// 找到最后一个匹配版本模式的字段
			versionIndex := -1
			for i := len(parts) - 1; i >= 0; i-- {
				if versionPattern.MatchString(parts[i]) {
					versionIndex = i
					break
				}
			}

			// 如果找到版本号
			if versionIndex >= 0 {
				version = parts[versionIndex]

				// 将版本号之前的所有字段用空格连接作为软件名
				if versionIndex > 0 {
					softwareParts := parts[:versionIndex]
					software = strings.Join(softwareParts, " ")
				} else if len(parts) == 1 {
					// 特殊情况：整个段落就是一个版本号（如 "SingleV001R002"）
					// 尝试从版本号中提取软件名部分
					versionStr := parts[0]
					versionMatch := versionPattern.FindString(versionStr)
					if versionMatch != "" {
						// 提取版本号前的部分作为软件名
						softwarePrefix := strings.Replace(versionStr, versionMatch, "", 1)
						if softwarePrefix != "" {
							software = softwarePrefix
						}
						version = versionMatch
					}
				}
			}
			break
		}
	}

	return software, version
}

// createCSVReport 创建 CSV 报告文件
func createCSVReport(filename string) error {
	// 等待所有 goroutine 完成
	wg.Wait()

	// 创建文件
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("无法创建文件 %s: %w", filename, err)
	}
	defer file.Close()

	// 写入 UTF-8 BOM
	_, err = file.Write([]byte{0xEF, 0xBB, 0xBF})
	if err != nil {
		return fmt.Errorf("无法写入 BOM: %w", err)
	}

	// 创建 CSV writer
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入表头
	header := []string{"Filename", "Software", "Version", "Full File Path", "Last Modified Time", "Archive Status", "Responsible Person", "Responsible Email", "Security Scan Result"}
	if err := writer.Write(header); err != nil {
		return fmt.Errorf("无法写入表头: %w", err)
	}

	// 如果没有找到文件，只写入表头
	if len(foundFiles) == 0 {
		logger.Println("未找到任何.exe文件，已创建空的CSV文件")
		return nil
	}

	// 写入数据行
	for _, fileInfo := range foundFiles {
		record := []string{
			fileInfo.Filename,
			fileInfo.Software,
			fileInfo.Version,
			fileInfo.RelativePath,
			fileInfo.ModTime,
			fileInfo.ArchiveStatus,
			fileInfo.ResponsiblePerson,
			fileInfo.ResponsibleEmail,
			fileInfo.SecurityScanResult,
		}
		if err := writer.Write(record); err != nil {
			return fmt.Errorf("无法写入数据行: %w", err)
		}
	}

	return nil
}

// startAntivirusScan 启动杀毒软件扫描归档目录
func startAntivirusScan(antivirusPath, targetDir string) {
	logger.Printf("\n启动杀毒软件扫描归档目录...\n")

	// 构建杀毒软件命令
	cmd := exec.Command(antivirusPath, "-s", targetDir)

	logger.Printf("执行命令：%s -s %s\n", antivirusPath, targetDir)

	// 设置杀毒软件在前台运行
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// 启动杀毒软件
	err := cmd.Start()
	if err != nil {
		logger.Printf("错误：无法启动杀毒软件：%s\n", err)
		logger.Printf("请检查杀毒软件路径是否正确，或手动扫描归档目录：%s\n", targetDir)
		return
	}

	logger.Printf("杀毒软件已启动，程序即将退出\n")
	logger.Printf("杀毒软件进程ID：%d\n", cmd.Process.Pid)

	// 程序退出，让杀毒软件继续在前台运行
	os.Exit(0)
}
