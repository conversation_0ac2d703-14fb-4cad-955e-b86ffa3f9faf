# Find EXE Files - Go 语言版本

一个现代化的企业级文件扫描和管理系统，采用模块化架构设计，提供完整的文件发现、安全检查和通知管理解决方案。

## 📋 功能概述

本系统由三个核心程序组成，支持完整的企业级文件管理工作流程：

**🔍 文件扫描程序** (`find_exe_files.exe`)
- 高性能并发文件搜索和软件信息提取
- 智能归档功能，支持跨文件系统复制
- 负责人管理，自动匹配文件责任人
- 集成杀毒软件，自动启动安全扫描

**🛡️ 安全检查程序** (`security_scan_reader.exe`)
- 集成火绒安全软件扫描结果
- 智能威胁文件识别和状态标记
- 支持 SQLite 数据库访问和 JSON 解析
- 自动更新 CSV 报告的安全状态字段

**📧 邮件通知程序** (`email_sender.exe`)
- 原生 Go SMTP 邮件发送，无外部依赖
- 支持分组邮件，根据负责人自动分组通知
- HTML 邮件正文，包含详细统计信息
- 灵活的收件人配置和邮件模板

**典型使用流程：**
1. 运行文件扫描程序进行文件发现和归档
2. 等待安全软件完成扫描（自动启动）
3. 运行安全检查程序更新威胁状态
4. 运行邮件通知程序发送分组报告

## 🚀 主要特性

- ✅ **模块化设计**：主程序、邮件发送、安全扫描功能独立分离
- ✅ **企业级特性**：负责人管理、分组邮件、安全扫描集成
- ✅ **中文支持**：原生 UTF-8 支持，完美处理中文路径和文件名
- ✅ **离线部署**：无外部依赖，单文件部署
- ✅ **静态编译**：可编译为完全独立的可执行文件
- ✅ **跨平台**：支持 Windows、Linux、macOS
- ✅ **高性能**：使用 goroutine 并发处理，速度更快
- ✅ **内存优化**：更低的内存占用和更快的启动速度
- ✅ **独立邮件**：邮件发送功能独立为单独程序，支持分组发送
- ✅ **安全集成**：支持火绒安全软件扫描结果集成
- ✅ **智能匹配**：自动软件名称提取和负责人分配

## 📁 项目结构

```
find_exe/
├── find_exe_files.go          # 主程序源代码（文件扫描、归档、负责人管理）
├── email_sender.go            # 邮件发送程序源代码（独立邮件功能）
├── security_scan_reader.go    # 安全扫描结果读取器（火绒集成）
├── go.mod                     # Go 模块文件
├── go.sum                     # Go 依赖锁定文件
├── config.json                # 主配置文件
├── owner_config.csv           # 负责人配置文件
├── detail.json                # 火绒扫描结果示例文件
├── README.md                  # 项目说明（本文件）
├── build/                     # 编译产物
│   ├── find_exe_files_windows_amd64.exe    # 主程序
│   ├── email_sender.exe                    # 邮件发送程序
│   ├── security_scan_reader.exe            # 安全扫描读取器
│   ├── find_exe_files_linux_amd64
│   └── find_exe_files_darwin_amd64
├── test/                      # 测试文件和数据
│   ├── test_extract_function.go
│   ├── test_email_function.ps1
│   ├── test_security_scan_integration.ps1
│   ├── test_owner_management.ps1
│   ├── test.ps1
│   ├── test_new_logic/
│   └── exe_files_report_*.csv
└── archive/                   # 历史文件和文档
    ├── *_IMPLEMENTATION.md    # 各功能实现说明文档
    └── (其他历史文件)
```

## 🔧 编译指令

### 基本编译（当前平台）
```bash
# 编译主程序
go build -o find_exe_files.exe find_exe_files.go

# 编译邮件发送程序
go build -o email_sender.exe email_sender.go

# 编译安全扫描读取器（需要 SQLite 依赖）
go build -o security_scan_reader.exe security_scan_reader.go
```

### 静态编译指南

#### 1. 主程序静态编译（无外部依赖）
```bash
# Windows 64位
$env:CGO_ENABLED = "0"
$env:GOOS = "windows"; $env:GOARCH = "amd64"
go build -a -ldflags "-w -s" -o "build/find_exe_files_windows_amd64.exe" find_exe_files.go

# Linux 64位
$env:CGO_ENABLED = "0"
$env:GOOS = "linux"; $env:GOARCH = "amd64"
go build -a -ldflags "-w -s" -o "build/find_exe_files_linux_amd64" find_exe_files.go

# macOS 64位
$env:CGO_ENABLED = "0"
$env:GOOS = "darwin"; $env:GOARCH = "amd64"
go build -a -ldflags "-w -s" -o "build/find_exe_files_darwin_amd64" find_exe_files.go
```

#### 2. 邮件发送程序静态编译（无外部依赖）
```bash
# Windows 64位
$env:CGO_ENABLED = "0"
$env:GOOS = "windows"; $env:GOARCH = "amd64"
go build -a -ldflags "-w -s" -o "build/email_sender_windows_amd64.exe" email_sender.go

# Linux 64位
$env:CGO_ENABLED = "0"
$env:GOOS = "linux"; $env:GOARCH = "amd64"
go build -a -ldflags "-w -s" -o "build/email_sender_linux_amd64" email_sender.go
```

#### 3. 安全扫描读取器编译（包含 SQLite 依赖）
```bash
# Windows 64位（需要 CGO）
$env:CGO_ENABLED = "1"
$env:GOOS = "windows"; $env:GOARCH = "amd64"
go build -a -ldflags "-w -s" -o "build/security_scan_reader_windows_amd64.exe" security_scan_reader.go

# Linux 64位（需要 CGO）
$env:CGO_ENABLED = "1"
$env:GOOS = "linux"; $env:GOARCH = "amd64"
go build -a -ldflags "-w -s" -o "build/security_scan_reader_linux_amd64" security_scan_reader.go
```

### 依赖管理
```bash
# 下载依赖
go mod download

# 整理依赖
go mod tidy

# 验证依赖
go mod verify
```

### 编译参数说明
- **`-a`**：强制重新构建所有包
- **`-ldflags "-w -s"`**：去除调试信息，减小文件大小
  - `-w`：去除 DWARF 调试信息
  - `-s`：去除符号表和调试信息
- **`CGO_ENABLED=0`**：禁用 CGO，确保静态链接（仅适用于无 C 依赖的程序）
- **`CGO_ENABLED=1`**：启用 CGO（适用于有 C 依赖的程序，如 SQLite）

### 第三方依赖打包说明

#### SQLite 依赖处理
安全扫描读取器使用 `github.com/mattn/go-sqlite3` 库，这是一个 CGO 库：

1. **Windows 编译**：
   ```bash
   # 需要 GCC 编译器（推荐使用 TDM-GCC 或 MinGW-w64）
   $env:CGO_ENABLED = "1"
   go build -a -ldflags "-w -s -extldflags '-static'" -o security_scan_reader.exe security_scan_reader.go
   ```

2. **Linux 编译**：
   ```bash
   # 静态链接所有依赖
   CGO_ENABLED=1 go build -a -ldflags "-w -s -linkmode external -extldflags '-static'" -o security_scan_reader security_scan_reader.go
   ```

3. **Docker 编译**（推荐用于 Linux 静态编译）：
   ```dockerfile
   FROM golang:1.21-alpine AS builder
   RUN apk add --no-cache gcc musl-dev sqlite-dev
   WORKDIR /app
   COPY . .
   RUN CGO_ENABLED=1 go build -a -ldflags "-w -s -linkmode external -extldflags '-static'" -o security_scan_reader security_scan_reader.go
   ```

#### 无依赖程序优势
主程序和邮件发送程序使用纯 Go 标准库，具有以下优势：
- ✅ **真正的静态编译**：无任何外部依赖
- ✅ **跨平台编译**：可在任何平台编译任何目标平台
- ✅ **部署简单**：单文件部署，无需安装运行时
- ✅ **启动快速**：无动态库加载开销

## 📖 使用方法

### 🎯 典型使用流程

#### 完整工作流程示例
以下是一个完整的企业级使用流程，包含配置准备、文件扫描、安全检查和邮件通知：

**步骤 1：配置准备**
```bash
# 1. 修改主配置文件
notepad config.json

# 2. 更新负责人配置文件
notepad owner_config.csv
```

**步骤 2：执行文件扫描**
```bash
# 运行主程序进行文件扫描和归档
./find_exe_files.exe -config config.json
```

**步骤 3：等待安全扫描完成**
```bash
# 如果启用了杀毒功能，等待火绒安全软件扫描完成
# 程序会自动启动火绒扫描归档目录
```

**步骤 4：读取安全扫描结果**
```bash
# 运行安全扫描结果读取器，更新 CSV 文件的安全状态
./security_scan_reader.exe
```

**步骤 5：发送邮件通知**
```bash
# 发送普通邮件
./email_sender.exe -csv exe_files_report_latest.csv -r "<EMAIL>"

# 或发送分组邮件（根据负责人自动分组）
./email_sender.exe -csv exe_files_report_latest.csv -grouped
```

### 📋 配置文件详解

#### 主配置文件 (config.json)
```json
{
  "scan_dir": "C:\\Program Files",
  "file_ext": [".exe", ".dll", ".sys"],
  "enable_archive": true,
  "archive_dir": "C:\\Backup\\Software_Archive",
  "enable_antivirus": true,
  "antivirus_path": "C:\\Program Files\\Huorong\\Sysdiag\\bin\\HipsMain.exe",
  "recipients": "<EMAIL>;<EMAIL>",
  "smtp_config": {
    "host": "smtp.company.com",
    "port": 25,
    "username": "",
    "password": "",
    "from": "<EMAIL>",
    "use_tls": false
  },
  "owner_config": "owner_config.csv"
}
```

#### 负责人配置文件 (owner_config.csv)
```csv
software_pattern,responsible_person,email
FIST SMS,涂伟健,<EMAIL>
Software A,张三,<EMAIL>
Single,李四,<EMAIL>
System,王五,<EMAIL>
Oracle,数据库团队,<EMAIL>
```

**字段说明：**
- `software_pattern`：软件名称匹配模式（支持部分匹配）
- `responsible_person`：负责人姓名
- `email`：负责人邮箱地址

### 🚀 快速开始

#### 基本使用
```bash
# 使用默认配置文件运行
./find_exe_files.exe

# 显示当前配置
./find_exe_files.exe -show-config

# 指定自定义配置文件
./find_exe_files.exe -config "custom_config.json"
```

#### 命令行快速配置
```bash
# 快速扫描指定目录
./find_exe_files.exe -scan-dir "C:\Program Files"

# 扫描并归档
./find_exe_files.exe -d "C:\Software" -archive -a "C:\Backup"

# 完整功能（扫描+归档+杀毒+负责人）
./find_exe_files.exe -d "C:\Program Files" -archive -a "C:\Backup" -antivirus -av "C:\AV\scan.exe" -owner-config "owner_config.csv"
```

### 命令行参数（可覆盖配置文件）
```bash
# 基本用法（使用配置文件默认值）
./find_exe_files.exe

# 覆盖扫描目录
./find_exe_files.exe -scan-dir "C:\Program Files"

# 覆盖文件扩展名（支持多个）
./find_exe_files.exe -file-ext ".exe,.dll,.sys"

# 启用归档功能
./find_exe_files.exe -enable-archive -archive-dir "C:\Backup"

# 启用杀毒扫描
./find_exe_files.exe -enable-antivirus -antivirus-path "C:\AV\scan.exe"

# 使用简写参数
./find_exe_files.exe -d "C:\Program Files" -e ".exe" -archive -a "C:\Backup" -antivirus -av "C:\AV\scan.exe"

# 完整功能组合
./find_exe_files.exe -d "C:\Program Files" -e ".exe,.dll" -archive -a "C:\Backup" -antivirus -av "C:\AV\scan.exe" -r "<EMAIL>"
```

### 参数说明
- **`-scan-dir` / `-d`**：扫描目录路径
- **`-file-ext` / `-e`**：文件后缀，多个后缀用逗号分隔（如 `.exe,.dll,.sys`）
- **`-enable-archive` / `-archive`**：是否启用归档功能（布尔值）
- **`-archive-dir` / `-a`**：归档目标目录路径
- **`-enable-antivirus` / `-antivirus`**：是否启用杀毒扫描（布尔值）
- **`-antivirus-path` / `-av`**：杀毒软件可执行文件路径
- **`-recipients` / `-r`**：邮件收件人，多个收件人用分号分隔
- **`-config` / `-c`**：指定配置文件路径（默认为 `./config.json`）
- **`-owner-config`**：负责人配置文件路径（预留参数）
- **`-show-config`**：显示当前有效配置

### 输出文件
程序会生成带时间戳的 CSV 文件：
```
exe_files_report_YYYYMMDD_HHMMSS.csv    # 主报告文件
exe_files_report_latest.csv             # 最新报告副本（供其他脚本使用）
```

CSV 文件包含以下列：
- **Filename**：文件名
- **Software**：软件名（从路径中提取）
- **Version**：版本号（匹配 V\d{3}R\d{3} 模式）
- **Full File Path**：完整文件路径
- **Last Modified Time**：最后修改时间
- **Archive Status**：归档状态（成功/失败/跳过）
- **Responsible Person**：负责人姓名
- **Responsible Email**：负责人邮箱地址
- **Security Scan Result**：安全扫描结果（威胁检测/安全/空）

## 📧 邮件发送功能

### 功能说明
邮件发送功能已独立为单独的程序 `email_sender.exe`，支持普通邮件和分组邮件发送，使用 Go 原生 SMTP 实现，无需外部依赖。

### 基本使用
```bash
# 发送邮件给单个收件人
./email_sender.exe -csv "exe_files_report_latest.csv" -r "<EMAIL>"

# 发送邮件给多个收件人
./email_sender.exe -csv "report.csv" -r "<EMAIL>;<EMAIL>"

# 使用配置文件中的收件人
./email_sender.exe -csv "exe_files_report_latest.csv"

# 显示邮件配置
./email_sender.exe -show-config
```

### 分组邮件发送
```bash
# 启用分组邮件（根据负责人邮箱自动分组）
./email_sender.exe -csv "exe_files_report_latest.csv" -grouped

# 分组邮件 + 指定收件人
./email_sender.exe -csv "report.csv" -grouped -r "<EMAIL>"
```

### 邮件内容特性
- **主题格式**：`.exe文件搜索结果 - 共找到X个文件 - YYYY-MM-DD`
- **HTML 正文**：包含搜索时间、文件数量、归档统计等信息
- **CSV 附件**：自动附加指定的 CSV 报告文件
- **UTF-8 编码**：完美支持中文邮件内容
- **日期标记**：邮件主题自动包含当前日期

### 分组邮件逻辑
1. **负责人分组**：根据 CSV 文件中的 `Responsible Email` 字段自动分组
2. **专门邮件**：每个负责人收到包含其负责文件的专门邮件
3. **汇总邮件**：配置中的收件人收到包含所有文件的汇总邮件
4. **临时文件**：为每个分组创建临时 CSV 文件，发送后自动清理

### 命令行参数
- **`-csv`**：指定 CSV 报告文件路径（默认：exe_files_report_latest.csv）
- **`-recipients` / `-r`**：邮件收件人，多个收件人用分号分隔
- **`-config` / `-c`**：指定配置文件路径（默认：./config.json）
- **`-grouped` / `-g`**：启用分组邮件发送
- **`-show-config`**：显示当前邮件配置

### 错误处理
- **配置文件错误**：详细的配置文件格式检查和错误提示
- **SMTP 连接失败**：提供具体的网络连接错误信息
- **附件文件不存在**：检查 CSV 文件是否存在
- **收件人格式错误**：验证邮箱地址格式
- **分组邮件失败**：单个分组失败不影响其他分组的发送

### 环境要求
- ✅ **无外部依赖**：使用 Go 原生 SMTP 库
- ✅ **配置文件**：需要正确配置 SMTP 服务器信息
- ✅ **网络连接**：需要能够访问 SMTP 服务器

## 🛡️ 安全扫描结果集成

### 功能说明
安全扫描结果集成功能通过独立的 `security_scan_reader.exe` 程序实现，支持读取火绒安全软件的扫描结果，并更新 CSV 文件中的安全扫描状态。

### 基本使用
```bash
# 读取火绒数据库并更新 CSV 文件
./security_scan_reader.exe

# 程序会自动：
# 1. 读取 exe_files_report_latest.csv 文件
# 2. 访问火绒数据库获取威胁文件列表
# 3. 匹配文件路径并更新安全扫描结果字段
# 4. 保存更新后的 CSV 文件
```

### 工作原理
1. **数据库访问**：读取火绒安全软件数据库 `C:\ProgramData\Huorong\Sysdiag\log.db`
2. **临时复制**：将数据库文件复制到临时目录避免文件锁定
3. **查询最新记录**：从 `HrLogV3_60` 表获取最新的扫描记录
4. **JSON 解析**：解析 `detail` 字段中的威胁文件列表
5. **路径匹配**：将威胁文件路径与 CSV 中的文件路径进行智能匹配
6. **状态更新**：更新匹配文件的安全扫描结果字段

### 安全扫描结果状态
- **`威胁检测`**：文件在火绒威胁列表中，存在安全风险
- **`安全`**：文件不在威胁列表中，通过安全检查
- **空字符串**：未进行安全扫描或扫描结果不可用

### 数据库结构
火绒安全软件使用 SQLite3 数据库存储扫描结果：
```sql
-- 查询最新扫描记录
SELECT detail FROM HrLogV3_60 ORDER BY ts DESC LIMIT 1;
```

威胁文件信息存储在 JSON 格式的 `detail` 字段中：
```json
{
  "detail": {
    "threat_list": [
      {
        "fn": "D:\\Project\\malware.exe",
        "det": "Virus/Trojan.Generic",
        "md5": "...",
        "sha256": "..."
      }
    ]
  }
}
```

### 路径匹配算法
```go
// 路径标准化
normalizedPath := strings.ToLower(strings.ReplaceAll(filePath, "\\", "/"))

// 智能匹配（支持部分路径匹配）
if strings.Contains(threatPath, normalizedPath) || strings.Contains(normalizedPath, threatPath) {
    // 标记为威胁检测
}
```

### 错误处理
- **数据库不存在**：显示警告，跳过安全扫描结果更新
- **权限不足**：提供权限错误的解决建议
- **CSV 文件格式错误**：验证 CSV 文件结构和字段
- **JSON 解析失败**：处理损坏的扫描结果数据
- **临时文件清理**：确保临时数据库文件被正确清理

### 使用场景
1. **企业安全审计**：定期扫描和检查文件安全状态
2. **合规性检查**：确保归档文件符合安全标准
3. **威胁响应**：快速识别和处理潜在威胁文件
4. **安全报告**：生成包含安全状态的详细报告

### 环境要求
- ✅ **火绒安全软件**：需要安装火绒安全软件并完成至少一次扫描
- ✅ **数据库访问权限**：需要读取火绒数据库文件的权限
- ✅ **SQLite 支持**：程序内置 SQLite 驱动，无需额外安装

## 📦 归档功能

### 功能说明
归档功能允许在搜索 .exe 文件的同时将文件复制到指定的归档目录，保持完整的目录结构。

### 使用方法
```bash
# 基本归档功能
./find_exe_files.exe -d "C:\Program Files" -archive "C:\Backup"

# 使用简写参数
./find_exe_files.exe -d "C:\Program Files" -a "C:\Backup"

# 归档到深层目录（自动创建）
./find_exe_files.exe -d "C:\Software" -archive "C:\Backup\2025\January"

# 结合邮件发送
./find_exe_files.exe -d "\\\\server\\share" -archive "C:\LocalBackup" -r "<EMAIL>"
```

### 核心特性
- **保持目录结构**：复制时保持相对于搜索根目录的完整目录结构
- **自动创建目录**：如果目标路径不存在，自动创建完整的目录结构
- **跨文件系统支持**：支持从网络共享、不同文件系统之间复制文件
- **并发处理**：使用 goroutine 并发复制文件，提高性能
- **智能跳过**：已存在的文件会被跳过，避免重复复制

### 归档统计
程序会显示详细的归档统计信息：
```
归档统计信息：
  总文件数：15
  成功复制：12
  跳过复制：2
  复制失败：1
  成功率：80.0%
```

### 错误处理
- **权限不足**：记录错误但继续处理其他文件
- **磁盘空间不足**：提供详细错误信息
- **网络问题**：处理网络共享访问问题
- **文件已存在**：智能跳过，避免覆盖

## 🛡️ 杀毒软件扫描功能

### 功能说明
杀毒软件扫描功能允许在归档操作完成后自动调用杀毒软件扫描归档目录，确保归档文件的安全性。

### 使用方法
```bash
# 归档并启动杀毒软件扫描
./find_exe_files.exe -d "C:\Program Files" -archive "C:\Backup" -antivirus "C:\Program Files\Antivirus\scanner.exe"

# 使用简写参数
./find_exe_files.exe -d "C:\Program Files" -a "C:\Backup" -av "C:\AV\scan.exe"

# 结合邮件发送
./find_exe_files.exe -d "C:\Software" -archive "C:\Backup" -antivirus "C:\AV\scan.exe" -r "<EMAIL>"

# 火绒安全软件示例
./find_exe_files.exe -d "C:\Program Files" -a "C:\Backup" -av "C:\Program Files\Huorong\Sysdiag\bin\HipsMain.exe"
```

### 核心特性
- **自动触发**：仅在归档成功且至少有一个文件被归档后执行
- **前台运行**：杀毒软件在前台运行，用户可以看到扫描进度
- **程序退出**：启动杀毒软件后，Go 程序自动退出
- **命令格式**：使用标准的 `[杀毒软件路径] -s [归档目录路径]` 格式
- **路径验证**：自动验证杀毒软件路径是否存在和可执行

### 执行流程
```
归档统计信息：
  总文件数：15
  成功复制：12
  跳过复制：2
  复制失败：1
  成功率：80.0%

启动杀毒软件扫描归档目录...
执行命令：C:\Program Files\Antivirus\scanner.exe -s C:\Backup
杀毒软件已启动，程序即将退出
杀毒软件进程ID：12345
```

### 错误处理
- **无归档功能**：显示警告信息，跳过杀毒扫描
- **路径不存在**：显示错误信息，程序退出
- **启动失败**：提供详细错误信息和手动扫描建议
- **无成功归档**：自动跳过杀毒扫描

## 🌍 中文支持

### 原生 UTF-8 支持
Go 语言原生支持 UTF-8，无需额外配置：
- **文件路径**：自动处理包含中文的目录和文件名
- **CSV 输出**：自动添加 UTF-8 BOM，确保 Excel 正确显示
- **控制台输出**：正确显示中文提示信息
- **邮件主题**：支持中文邮件主题

### 测试中文支持
```bash
# 创建中文测试环境
mkdir "测试目录\软件V001R002"
echo "test" > "测试目录\软件V001R002\中文程序.exe"

# 运行测试
./find_exe_files.exe "测试目录"
```

## 🔍 软件名和版本号提取

### 提取逻辑
程序使用改进的算法从路径中提取软件名和版本号：

1. 在路径段中找到包含版本号模式 `V\d{3}R\d{3}` 的段落
2. 使用空格、下划线、连字符 `[ _-]` 分割该段落
3. 从分割结果中找到**最后一个**匹配版本模式的字段作为版本号
4. 将该版本号**之前的所有字段**用空格连接作为软件名

### 提取示例
- `FIST SMS V100R002B02D040` → 软件名：`"FIST SMS"`，版本号：`"V100R002B02D040"`
- `Software_A_V001R002` → 软件名：`"Software A"`，版本号：`"V001R002"`
- `Multi Word Software V123R456` → 软件名：`"Multi Word Software"`，版本号：`"V123R456"`

## 📊 性能特性

| 特性指标 | 传统脚本方案 | Go 模块化方案 | 优势 |
|----------|-------------|--------------|------|
| 启动时间 | ~2-3秒 | ~0.1秒 | **极速启动** |
| 内存占用 | ~50-100MB | ~10-20MB | **轻量级** |
| 处理方式 | 单线程 | 并发处理 | **高性能** |
| 部署方式 | 需要运行时环境 | 单文件部署 | **极简部署** |
| 程序大小 | 运行时 + 依赖 | ~8-12MB | **独立可执行** |
| 邮件功能 | 外部脚本依赖 | 原生 SMTP | **无外部依赖** |
| 架构设计 | 单一脚本 | 模块化程序 | **易于维护** |

## 🧪 测试

### 运行测试
```bash
# 测试软件名和版本号提取功能
go run test/test_extract_function.go

# 测试邮件发送功能
powershell -ExecutionPolicy Bypass -File test/test_email_function.ps1

# 测试负责人管理功能
powershell -ExecutionPolicy Bypass -File test/test_owner_management.ps1

# 测试安全扫描结果集成功能
powershell -ExecutionPolicy Bypass -File test/test_security_scan_integration.ps1

# 综合功能测试
powershell -ExecutionPolicy Bypass -File test/test.ps1
```

### 测试覆盖
- ✅ 中文路径和文件名处理
- ✅ 软件名和版本号提取算法
- ✅ 邮件发送功能（普通邮件和分组邮件）
- ✅ 负责人管理和分组逻辑
- ✅ 安全扫描结果集成
- ✅ 错误处理和边界情况
- ✅ 跨平台兼容性
- ✅ 模块化程序集成测试

## 📦 部署

### 完整部署包
```
deployment/
├── find_exe_files.exe          # 主程序
├── email_sender.exe            # 邮件发送程序
├── security_scan_reader.exe    # 安全扫描读取器
├── config.json                 # 主配置文件
├── owner_config.csv            # 负责人配置文件
└── README.txt                  # 使用说明
```

### 离线环境部署
1. 将完整部署包复制到目标机器
2. 根据环境修改 `config.json` 和 `owner_config.csv`
3. 无需安装任何运行时或依赖
4. 直接运行即可

### 企业环境部署示例
```bash
# 1. 创建工作目录
mkdir C:\Tools\FileScanner
cd C:\Tools\FileScanner

# 2. 复制程序文件
copy build\find_exe_files_windows_amd64.exe find_exe_files.exe
copy build\email_sender.exe email_sender.exe
copy build\security_scan_reader.exe security_scan_reader.exe

# 3. 复制配置文件
copy config.json config.json
copy owner_config.csv owner_config.csv

# 4. 创建批处理脚本
echo @echo off > scan_and_notify.bat
echo echo 开始文件扫描... >> scan_and_notify.bat
echo find_exe_files.exe -config config.json >> scan_and_notify.bat
echo echo 更新安全扫描结果... >> scan_and_notify.bat
echo security_scan_reader.exe >> scan_and_notify.bat
echo echo 发送邮件通知... >> scan_and_notify.bat
echo email_sender.exe -csv exe_files_report_latest.csv -grouped >> scan_and_notify.bat
echo echo 任务完成！ >> scan_and_notify.bat
echo pause >> scan_and_notify.bat

# 5. 运行完整流程
scan_and_notify.bat
```

### 定时任务部署
```bash
# Windows 任务计划程序
schtasks /create /tn "FileScanner" /tr "C:\Tools\FileScanner\scan_and_notify.bat" /sc daily /st 02:00

# Linux crontab
0 2 * * * /opt/filescanner/scan_and_notify.sh
```

## 🐛 故障排除

### 常见问题

1. **编译失败**
   ```
   解决：确保 Go 版本 >= 1.19，运行 go mod tidy
   ```

2. **权限错误**
   ```
   解决：以管理员权限运行程序
   ```

3. **中文乱码**
   ```
   解决：程序已自动处理 UTF-8 编码，确保使用支持 UTF-8 的编辑器
   ```

4. **邮件发送失败**
   ```
   解决：检查 SMTP 服务器配置，确保网络连接正常
   ```

## 📝 更新日志

### v3.0.0 (当前版本) - 模块化重构版本
- ✅ **模块化重构**：将邮件发送功能拆分为独立程序
- ✅ **安全扫描集成**：新增火绒安全软件扫描结果集成功能
- ✅ **负责人管理**：实现文件负责人分配和分组邮件通知
- ✅ **邮件功能增强**：支持分组邮件发送，邮件主题包含日期
- ✅ **静态编译优化**：完善静态编译指南，支持第三方依赖打包
- ✅ **配置系统完善**：支持 SMTP 配置、负责人配置等
- ✅ **文档完善**：详细的使用流程和部署指南
- ✅ **错误处理优化**：更好的错误提示和异常处理

### v2.0.0 - 功能完整版本
- ✅ 实现完整的文件扫描和管理功能
- ✅ 改进软件名和版本号提取算法
- ✅ 实现完整的邮件发送功能
- ✅ 添加并发处理支持
- ✅ 完善中文支持和错误处理
- ✅ 提供跨平台编译版本
- ✅ 重新组织项目结构

### v1.0.0 - 基础版本
- 基础功能实现
- 基本的文件搜索和 CSV 生成

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

本项目采用开源许可证，欢迎在企业和个人项目中使用。

---

**注意**：本系统采用现代化的模块化架构设计，特别适合企业级部署环境，提供高性能、高可靠性的文件管理解决方案。
